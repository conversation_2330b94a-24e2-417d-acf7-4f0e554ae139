import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import type { ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Modal, Typography } from 'antd';
import React, { memo, useRef, useState } from 'react';
import AddModal from './components/AddModal';
import type { TlistItem } from './services';
import { exportCompanyList, getList, remove } from './services';

const PremiumCompany = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState({} as any);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const columns: ProColumns<TlistItem>[] = [
    {
      title: '保费收款公司id',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '保费收款公司名称',
      dataIndex: 'companyName',
    },
    {
      title: '保费收款公司账号',
      dataIndex: 'paymentAccountNo',
    },
    {
      title: '保费收款账号开户行',
      dataIndex: 'subBranchName',
    },

    {
      title: '保司简称',
      dataIndex: 'insuranceCompanyShortName',
      search: false,
    },
    {
      title: '地区',
      dataIndex: 'areaName',
      search: false,
    },
    {
      title: '联行号',
      dataIndex: 'bankNo',
    },
    {
      title: '所属渠道',
      dataIndex: 'channelList',
      search: false,
      render: (_, record) => {
        const channelName = record?.channelList?.map((item) => item?.channelName) || [];
        let dom = <>-</>;
        if (channelName?.length > 0) {
          dom = (
            <>
              <Typography.Text
                style={{ width: 200 }}
                ellipsis={{
                  tooltip: {
                    title: <div dangerouslySetInnerHTML={{ __html: channelName?.join('<br/>') }} />,
                    placement: 'bottomRight',
                  },
                  suffix: (
                    <>
                      等<span style={{ color: '#1890ff' }}>{channelName?.length}条</span>
                    </>
                  ),
                }}
              >
                {channelName.join('、')}
              </Typography.Text>
            </>
          );
        }
        return <>{dom}</>;
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 160,
      render: (_, record) => {
        const { companyName, id, channelList } = record;
        const channelCodeList = channelList?.map((item) => item?.channelCode);
        return [
          <Button
            type="link"
            key="edit"
            onClick={() => {
              setCurrentRecord({ ...record, channelCodeList });
              setVisible(true);
            }}
          >
            编辑
          </Button>,
          <Button
            type="link"
            key="delete"
            onClick={() => {
              Modal.confirm({
                title: '删除',
                content: `你确定要删除-${companyName}吗`,
                async onOk() {
                  await remove(id!);
                  message.success('删除成功');
                  actionRef.current?.reload();
                },
              });
            }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  async function getSearchDataTotal() {
    const searchParams = filterProps(formRef.current?.getFieldsFormatValue?.());
    const data = await getList({
      ...searchParams,
      pageNumber: 1,
    });
    return data?.total;
  }

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable
          actionRef={actionRef}
          formRef={formRef}
          rowKey="id"
          request={async (params) => {
            const { current = 1, pageSize = 10, ...rest } = params;
            const data = await getList({ pageSize, pageNumber: current, ...rest });
            const { total, data: list } = data;
            return {
              total,
              data: list,
              success: true,
            };
          }}
          // dataSource={[{ channelCodeList: ['1222222222222222', '111111111111112'] }]}
          columns={columns}
          search={{ labelWidth: 'auto', defaultCollapsed: false }}
          toolBarRender={() => {
            return [
              <AsyncExport
                key="export"
                getSearchDataTotal={getSearchDataTotal}
                getSearchParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return removeBlankFromObject(
                    filterProps({
                      ...params,
                    }),
                  );
                }}
                trigger={<Button type="primary">导出</Button>}
                exportAsync={exportCompanyList}
                taskCode={[ItaskCodeEnValueEnum.PREMIUM_COMPANY]}
              />,

              <Button
                key="add"
                type="primary"
                onClick={() => {
                  setCurrentRecord({});
                  setVisible(true);
                }}
              >
                新增
              </Button>,
            ];
          }}
        />
      </PageContainer>
      <AddModal
        visible={visible}
        setVisible={setVisible}
        record={currentRecord}
        actionRef={actionRef}
      />
    </>
  );
};

export default memo(PremiumCompany);
