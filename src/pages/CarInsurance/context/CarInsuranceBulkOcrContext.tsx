import { EcarInsuranceStatus } from '@/utils/bankend/enum';
import { useModel, useRequest } from '@umijs/max';
import { message } from 'antd';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useRef,
  useState,
} from 'react';
import { checkMatchProcess, updateBulkOcrStatus } from '../services';
import { BULK_UPLOAD_EVENT_ACTION, BULK_UPLOAD_STATUS } from '../type';

export interface BatchOperationStatusProps {
  isProcessing: boolean; // 是否正在进行批量操作
  isMatching: boolean; // 是否正在匹配
  isMatchComplete: boolean; // 是否匹配完成
  matchProgress: number; // 匹配进度 0-100
  startTime: number; // 开始匹配时间
  endTime: number; // 结束匹配时间
  totalCount: number; // 总数量
  matchedTotalCount: number; // 已匹配数量
  businessNo?: string; // ocr业务编号
  matchInProcessTerminated: boolean; // 是否匹配中异常中断
}

export interface BatchOcrDataIF {
  carInfoDTOList: any[];
  notMatchFileDTOList: any[];
  toBeMatchFileDTOList: any[]; // 四个步骤的文件列表，分别为各自的二维数组，例如[[投保单], [购车发票], [合格证], [行驶证]]
  status: BULK_UPLOAD_STATUS | null;
  businessNo?: string | undefined;
  groupFlag?: number;
}

export const CarInsuranceBulkOcrContext = createContext<{
  batchOperationStatus: BatchOperationStatusProps;
  updateBatchOperationStatus: (
    status:
      | BatchOperationStatusProps
      | ((prev: BatchOperationStatusProps) => BatchOperationStatusProps),
  ) => void;
  batchOcrData: BatchOcrDataIF;
  updateBatchOcrData: (data: BatchOcrDataIF | ((prev: BatchOcrDataIF) => BatchOcrDataIF)) => void;
  handleGiveUpOrReAction: (event: BULK_UPLOAD_EVENT_ACTION, businessNo?: string) => Promise<void>;
  checkMatchProcessCallback: (orderNo?: string) => void;
  batchFileDTO: {
    policyFiles: any[];
    purchaseFiles: any[];
    creditFiles: any[];
    licenseFiles: any[];
  };
  dispatchBatchFiles: (action: any) => void;
  actionLoading: boolean;
  updateActionLoading: (loading: boolean) => void;
  processLoading: boolean;
  batchAddModalRef: React.RefObject<any> | null | undefined;
}>({
  batchOperationStatus: {
    isProcessing: false,
    isMatching: false,
    isMatchComplete: false,
    startTime: 0,
    endTime: 0,
    matchProgress: 0,
    totalCount: 0,
    matchedTotalCount: 0,
    matchInProcessTerminated: false,
  },
  updateBatchOperationStatus: () => {},
  batchOcrData: {
    carInfoDTOList: [],
    notMatchFileDTOList: [],
    toBeMatchFileDTOList: [],
    status: null,
  },
  updateBatchOcrData: () => {},
  handleGiveUpOrReAction: async () => {},
  checkMatchProcessCallback: () => {},
  batchFileDTO: {
    policyFiles: [],
    purchaseFiles: [],
    creditFiles: [],
    licenseFiles: [],
  },
  dispatchBatchFiles: () => {},
  actionLoading: false,
  updateActionLoading: () => {},
  processLoading: false,
  batchAddModalRef: null,
});

export const useCarInsuranceBulkOcrContext = () => {
  return useContext(CarInsuranceBulkOcrContext);
};

// 批量上传文件状态管理
function batchFilesUpdateReducer(state, action) {
  switch (action.type) {
    case 'add_policy':
      return { ...state, policyFiles: action.payload.fileList };
    case 'delete_policy':
      return {
        ...state,
        policyFiles: state.policyFiles.filter((item) => item.uid !== action.payload.uid),
      };
    case 'add_purchase':
      return { ...state, purchaseFiles: action.payload.fileList };
    case 'delete_purchase':
      return {
        ...state,
        purchaseFiles: state.purchaseFiles.filter((item) => item.uid !== action.payload.uid),
      };
    case 'add_credit':
      return { ...state, creditFiles: action.payload.fileList };
    case 'delete_credit':
      return {
        ...state,
        creditFiles: state.creditFiles.filter((item) => item.uid !== action.payload.uid),
      };
    case 'add_license':
      return { ...state, licenseFiles: action.payload.fileList };
    case 'delete_license':
      return {
        ...state,
        licenseFiles: state.licenseFiles.filter((item) => item.uid !== action.payload.uid),
      };
    case 'bulk-update':
      return { ...state, ...action.payload };
    case 'reset':
      return {
        policyFiles: [],
        purchaseFiles: [],
        creditFiles: [],
        licenseFiles: [],
      };
    default:
      throw new Error('Invalid action type');
  }
}

export const CarInsuranceBulkOcrProvider = (props: { children: React.ReactNode }) => {
  const { children } = props;
  const { detailData } = useModel('CarInsurance.carInsurance');
  const { orderNo, orderStatus } = detailData;
  const search = new URLSearchParams(window.location.search);
  const multiplexOrderNo = search.get('multiplexOrderNo');
  const n_OrderNo = search.get('orderNo');

  const batchAddModalRef = useRef<any>(null);

  const [batchFileDTO, dispatchBatchFiles] = useReducer(batchFilesUpdateReducer, {
    policyFiles: [],
    purchaseFiles: [],
    creditFiles: [],
    licenseFiles: [],
  });

  // 批量操作状态管理
  const [batchOperationStatus, setBatchOperationStatus] = useState<BatchOperationStatusProps>({
    isProcessing: false,
    isMatching: false,
    isMatchComplete: false,
    startTime: 0,
    endTime: 0,
    matchProgress: 0,
    totalCount: 0,
    matchedTotalCount: 0,
    matchInProcessTerminated: false,
  });

  // loading
  const [processLoading, setProcessLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // 完整的批量ocr信息
  const [batchOcrData, setBatchOcrData] = useState<BatchOcrDataIF>({
    carInfoDTOList: [],
    notMatchFileDTOList: [],
    toBeMatchFileDTOList: [],
    status: null,
    groupFlag: 2, // 默认否
  });

  // 检查函数
  const checkMatchProcessCallback = useCallback(
    async (cancel?: () => void, in_orderNo?: string) => {
      try {
        const curentOrderNo = in_orderNo || orderNo;
        if (!curentOrderNo) return;
        setProcessLoading(true);

        console.log('curentOrderNo', curentOrderNo);
        const res = await checkMatchProcess({
          orderNo: curentOrderNo,
        });
        if (res?.data) {
          const {
            status,
            totalCount,
            matchedTotalCount,
            businessNo,
            startTime,
            endTime,
            matchInProcessTerminated,
            // groupFlag, // 是否团单
            // carInfoDTOList, // 车辆信息
            // notMatchFileDTOList, // 未匹配的文件
          } = res.data;
          const progress = totalCount > 0 ? Math.round((matchedTotalCount / totalCount) * 100) : 0;

          setBatchOperationStatus((prev) => ({
            ...prev,
            matchProgress: progress,
            totalCount: totalCount || 0,
            matchedTotalCount: matchedTotalCount || 0,
            businessNo,
            startTime,
            endTime,
            isMatching: status === BULK_UPLOAD_STATUS.MATCH_IN_PROGRESS,
            matchInProcessTerminated,
            isProcessing: status === BULK_UPLOAD_STATUS.TO_BE_MATCH,
            isMatchComplete: status === BULK_UPLOAD_STATUS.MATCH_COMPLETE,
          }));
          setBatchOcrData(res.data);

          // 如果匹配完成或终止，停止轮询
          if (
            [
              BULK_UPLOAD_STATUS.MATCH_COMPLETE,
              BULK_UPLOAD_STATUS.MATCH_TERMINATED,
              BULK_UPLOAD_STATUS.MATCH_COMPLETE_USED,
            ].includes(status) ||
            (status === BULK_UPLOAD_STATUS.MATCH_COMPLETE && matchInProcessTerminated)
          ) {
            cancel?.();
            setProcessLoading(false);
          }
        }
      } catch (error) {
        console.error('获取ocr进度失败:', error);
        return Promise.reject(error);
      } finally {
        setProcessLoading(false);
      }
    },
    [orderNo],
  );

  // 检查批量上传匹配进度
  const { run: runCheckProgress, cancel } = useRequest(() => checkMatchProcessCallback(cancel), {
    pollingInterval: 2000, // 轮询间隔
    debounceInterval: 1000, // 防抖，防止模块短时间内多次请求
    manual: true, // 初始化受控
  });

  // 开始轮询检查匹配状态，只有草稿和审核驳回才需要
  useEffect(() => {
    if (
      orderNo &&
      batchOperationStatus.isMatching &&
      !batchOperationStatus.matchInProcessTerminated &&
      [EcarInsuranceStatus.DRAFT, EcarInsuranceStatus.REJECT].includes(orderStatus)
    ) {
      runCheckProgress();
    }
    return () => {
      cancel?.();
    };
  }, [
    runCheckProgress,
    cancel,
    batchOperationStatus.isMatching,
    batchOperationStatus.matchInProcessTerminated,
    orderNo,
    orderStatus,
  ]);

  // 进来先调用获取一次状态，只有草稿和审核驳回才需要
  useEffect(() => {
    // 未匹配或匹配结束状态都会触发一次
    if (
      n_OrderNo &&
      !batchOperationStatus.isMatching &&
      !batchOperationStatus.matchInProcessTerminated &&
      [EcarInsuranceStatus.DRAFT, EcarInsuranceStatus.REJECT].includes(orderStatus) &&
      !multiplexOrderNo // 不是复用的草稿时调用
    ) {
      console.log('touched in context 286', n_OrderNo);
      checkMatchProcessCallback(cancel, n_OrderNo);
    }
  }, [checkMatchProcessCallback, orderStatus, multiplexOrderNo, n_OrderNo]);

  // 更新文件列表
  useEffect(() => {
    // 匹配中或匹配完成不触发下列state更新，性能优化
    if (batchOperationStatus.isMatching || batchOperationStatus.isMatchComplete) return;

    if (!!batchOcrData) {
      dispatchBatchFiles({
        type: 'bulk-update',
        payload: {
          policyFiles: batchOcrData?.toBeMatchFileDTOList?.[0] || [],
          purchaseFiles: batchOcrData?.toBeMatchFileDTOList?.[1] || [],
          creditFiles: batchOcrData?.toBeMatchFileDTOList?.[2] || [],
          licenseFiles: batchOcrData?.toBeMatchFileDTOList?.[3] || [],
        },
      });
    }
  }, [batchOcrData, dispatchBatchFiles]);

  // 处理放弃或更新等操作
  const handleGiveUpOrReAction = useCallback(
    async (event: BULK_UPLOAD_EVENT_ACTION, businessNo?: string) => {
      try {
        // 调用放弃操作接口 传递event GIVE_UP_MATCH = 放弃操作，TERMINATED_MATCH = 结束匹配
        const policyLen = batchOcrData?.toBeMatchFileDTOList?.[0]?.length || 0;
        const purchaseLen = batchOcrData?.toBeMatchFileDTOList?.[1]?.length || 0;
        const creditLen = batchOcrData?.toBeMatchFileDTOList?.[2]?.length || 0;
        const licenseLen = batchOcrData?.toBeMatchFileDTOList?.[3]?.length || 0;
        const counts = policyLen + purchaseLen + creditLen + licenseLen;
        if (counts > 500) {
          message.warning('上传文件不能为空或超过500个');
          return;
        }
        setActionLoading(true);
        console.log('event', event);
        const res = await updateBulkOcrStatus({
          businessNo: businessNo || batchOperationStatus.businessNo!,
          event,
        });

        console.log('updateBulkOcrStatus res', res);

        if (res?.ret === 0) {
          // 如果 当前任务为中断或者开始匹配，当用户触发继续开始 时，则重新开始轮训
          if (
            [
              BULK_UPLOAD_EVENT_ACTION.START_MATCH,
              BULK_UPLOAD_EVENT_ACTION.CONTINUE_MATCH,
            ].includes(event)
          ) {
            runCheckProgress();
            batchAddModalRef?.current?.hide();
            return;
          }
          console.log('touched in context 346');
          // 重新更新ocr结果
          checkMatchProcessCallback(cancel);

          // 完成匹配并已使用 或者 放弃操作时，关闭弹窗
          if (
            [
              BULK_UPLOAD_EVENT_ACTION.COMPLETE_MATCH_USED,
              BULK_UPLOAD_EVENT_ACTION.GIVE_UP_MATCH,
            ].includes(event)
          ) {
            batchAddModalRef?.current?.hide();
          }
        }
      } catch (e) {
        console.error(e);
        throw e; // 重新抛出异常，让调用方知道操作失败
      } finally {
        setActionLoading(false);
      }
    },
    [
      cancel,
      batchOperationStatus,
      batchOcrData,
      runCheckProgress,
      checkMatchProcessCallback,
      batchAddModalRef,
    ],
  );

  return (
    <CarInsuranceBulkOcrContext.Provider
      value={{
        batchOperationStatus,
        updateBatchOperationStatus: setBatchOperationStatus,
        batchOcrData,
        updateBatchOcrData: setBatchOcrData,
        handleGiveUpOrReAction,
        checkMatchProcessCallback: (fb_OrderNo?: string) =>
          checkMatchProcessCallback(cancel, fb_OrderNo),
        batchFileDTO,
        dispatchBatchFiles,
        actionLoading,
        updateActionLoading: setActionLoading,
        processLoading,
        batchAddModalRef,
      }}
    >
      {children}
    </CarInsuranceBulkOcrContext.Provider>
  );
};
