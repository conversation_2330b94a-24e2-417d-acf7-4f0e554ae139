import React, { memo, useEffect, useState } from 'react';
import { getCityList } from '../services';

import { Form, FormInstance } from 'antd';
import styles from '../styles/index.less';
import UploadIdCard from './UploadIdCard';

import { ProForm, ProFormDependency, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Icity, IloanPersonalInfo } from '../type';
import OverdueAlert from './OverdueAlert';

type Props = {
  loanPersonalInfoForm: FormInstance;
};
const LoanPersonal: React.FC<Props> = (props) => {
  const [cityList, setCityList] = useState<Icity>([]);
  const { isEditable, detailData } = useModel('CarInsurance.carInsurance');
  const { loanPersonalInfoForm } = props;
  async function getCitys() {
    try {
      const cityList = await getCityList();
      setCityList(cityList);
    } catch (error) {
      // 获取城市失败
    }
  }

  useEffect(() => {
    getCitys();
  }, []);

  useEffect(() => {
    const { personalInfo, orderNo } = detailData;
    if (orderNo) {
      loanPersonalInfoForm.setFieldsValue(personalInfo);
    }
  }, [loanPersonalInfoForm, detailData]);

  return (
    <>
      <h3>借款人信息</h3>
      <OverdueAlert />
      <ProForm<IloanPersonalInfo>
        labelWrap={true}
        form={loanPersonalInfoForm}
        layout="horizontal"
        submitter={{
          render: () => null,
        }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
      >
        <div style={{ display: 'flex', width: '100%' }}>
          <div className={styles['id-card-front']} style={{ width: '100%' }}>
            <div>
              <span style={{ color: 'red' }}>*</span> 身份证 - 人像面
              <a>需原件拍照,四角齐全,图片需小于5M,格式为.jpg,.jpeg,.png</a>
            </div>
            <Form.Item name="identityCardUrl" rules={[{ required: true }]}>
              <UploadIdCard
                longUrl={loanPersonalInfoForm.getFieldsValue(true)?.identityCardLongUrl}
                // idCardImgStyle={{ width: 500, height: 350 }}
              />
            </Form.Item>
          </div>
          <div className={styles['id-card-back']} style={{ width: '100%' }}>
            <div>
              <span style={{ color: 'red' }}>*</span> 身份证 - 国徽面
              <a>需原件拍照,四角齐全,图片需小于5M,格式为.jpg,.jpeg,.png</a>
            </div>
            <Form.Item rules={[{ required: true }]} name={'reverseIdentityCardUrl'}>
              <UploadIdCard
                longUrl={loanPersonalInfoForm.getFieldsValue(true)?.reverseIdentityCardLongUrl}
                // idCardImgStyle={{ width: 350, height: 150 }}
              />
            </Form.Item>
          </div>
          <div style={{ minWidth: 370, maxWidth: 370 }}>
            <ProFormText
              name={'name'}
              label="借款人姓名"
              placeholder="请输入"
              disabled={!isEditable}
              rules={[{ required: true }]}
            />
            <ProFormText
              fieldProps={{ inputMode: 'numeric' }}
              name={'idNo'}
              disabled={!isEditable}
              label="借款人身份证号"
              placeholder="请输入"
              transform={(value: string) => {
                return { authorizerCertiNo: value?.trim() };
              }}
              rules={[{ required: true }]}
            />
            <ProFormText
              name={'phone'}
              disabled={!isEditable}
              label="借款人联系电话"
              placeholder="请输入"
              rules={[{ required: true }]}
            />

            <Form.Item
              label={
                <div>
                  <span style={{ color: 'red' }}>*</span>借款人地址
                </div>
              }
              style={{ marginBottom: 0 }}
              rules={[{ required: true }]}
            >
              <div style={{ display: 'flex', gap: 10 }}>
                <ProFormSelect
                  name={'province'}
                  disabled={!isEditable}
                  fieldProps={{
                    showSearch: true,
                    onChange() {
                      loanPersonalInfoForm.setFieldsValue({
                        city: undefined,
                        address: undefined,
                      });
                    },
                  }}
                  label=""
                  placeholder="省份"
                  valueEnum={cityList.reduce((pre, cur) => {
                    const { name } = cur;
                    return { ...pre, [name]: name };
                  }, {})}
                  rules={[{ required: true }]}
                />

                <ProFormDependency name={['province']}>
                  {({ province }) => {
                    const valueEnum = cityList
                      .find((item) => item.name === province)
                      ?.children?.reduce((pre, cur) => {
                        const { name } = cur;
                        return { ...pre, [name]: name };
                      }, {});

                    return (
                      <ProFormSelect
                        name={'city'}
                        label=""
                        disabled={!isEditable}
                        placeholder="城市"
                        fieldProps={{
                          showSearch: true,
                        }}
                        rules={[{ required: true }]}
                        valueEnum={valueEnum}
                      />
                    );
                  }}
                </ProFormDependency>
              </div>
              <ProFormText
                name={'address'}
                label=""
                placeholder="详细地址"
                disabled={!isEditable}
                rules={[{ required: true }]}
              />
            </Form.Item>

            <ProFormText
              readonly
              label="在途车辆数"
              required
              {...{
                render() {
                  const value = detailData?.carInTransitNum || 0;
                  return (
                    <>
                      <span className={value > 20 && styles['on-way-car-count']}>{value}</span>辆
                    </>
                  );
                },
              }}
            />
          </div>
        </div>
      </ProForm>
    </>
  );
};

export default memo(LoanPersonal);
