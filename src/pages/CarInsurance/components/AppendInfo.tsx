import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import { EcarInsuranceStatus } from '@/utils/bankend/enum';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormUploadButton } from '@ant-design/pro-form';
import { useAccess, useModel } from '@umijs/max';
import { Button, message } from 'antd';
import React, { memo, useEffect, useMemo, useRef } from 'react';
import { saveExtend } from '../services';
import styles from '../styles/index.less';
import { getBaseUploadAction } from '../utils';

interface AppendInfoProps {}
const AppendInfo: React.FC<AppendInfoProps> = memo(() => {
  const appendInfoForm = useRef<ProFormInstance>();
  const { detailData } = useModel('CarInsurance.carInsurance');
  const { afterInformationList, orderNo } = detailData;
  const access = useAccess();
  useEffect(() => {
    appendInfoForm?.current?.setFieldsValue({
      otherInformationList: afterInformationList?.map((item, index) => ({
        uid: index,
        status: 'done',
        url: item.netWorkPath,
        name: item.fileName,
        shortOssUrl: item.shortOssUrl,
      })),
    });
  }, [appendInfoForm, afterInformationList]);
  const previewRef = useRef<ImagePreviewInstance>(null);
  const handlePreview = async (file: any) => {
    const values = appendInfoForm?.current?.getFieldsValue();
    previewRef.current?.previewFile({
      url: file?.url,
      urlList: values?.afterInformationList?.map((item: any) => item?.url),
    });
  };

  const mode = useMemo(() => {
    if (access.hasAccess('upload_after_information_detail_car_insurance_list')) {
      return 'edit';
    } else if (access.hasAccess('delete_after_information_detail_car_insurance_list')) {
      return 'delete';
    }
    return 'read';
  }, [access]);
  const displayComp = useMemo(() => {
    const { orderStatus } = detailData;
    return [
      EcarInsuranceStatus.SIGN,
      EcarInsuranceStatus.TO_BE_DOWN_PAYMENT,
      EcarInsuranceStatus.TO_BE_DOWN_PAYMENT_AUDIT,
      EcarInsuranceStatus.TO_BE_DOWN_PAYMENT_REJECT,
      EcarInsuranceStatus.LOAN_PENDING,
      EcarInsuranceStatus.REPAYING,
      EcarInsuranceStatus.OVERDUE,
      EcarInsuranceStatus.SETTLE,
      EcarInsuranceStatus.OVERDUE_SETTLE,
      EcarInsuranceStatus.EARLY_SETTLE,
      EcarInsuranceStatus.SURRENDERED,
    ].includes(orderStatus);
  }, [detailData]);
  const handleSubmit = () => {
    appendInfoForm.current
      ?.validateFieldsReturnFormatValue?.()
      .then(({ otherInformationList }) => {
        return saveExtend({
          orderNo,
          afterInformationList: otherInformationList.map((item) => {
            return {
              fileName: item?.name,
              shortOssUrl: item?.shortOssUrl,
              netWorkPath: item?.url,
            };
          }),
        });
      })
      .then(() => {
        message.success('操作成功');
      });
  };
  return displayComp ? (
    <div className={styles['append-info-container']}>
      <h3>后补材料</h3>
      <ProForm
        layout="horizontal"
        formRef={appendInfoForm}
        submitter={{ render: () => null }}
        disabled={mode === 'read'}
      >
        <ProFormUploadButton
          name="otherInformationList"
          action={getBaseUploadAction('/base/oss/common/uploadfile')}
          rules={[{ required: true, message: '后补材料不能为空' }]}
          fieldProps={{
            data: {
              attachment: false,
              acl: 'PUBLIC_READ',
              destPath: 'insurance/policy/vehicleLicense/',
            },
            headers: { ...getAuthHeaders() },
            name: 'file',

            multiple: true,
            onPreview: handlePreview,
            onChange: ({ fileList }) => {
              fileList.forEach((file) => {
                if (file?.status === 'done') {
                  const { response = {} } = file;
                  const { code, data = {} } = response;
                  if (code === 200) {
                    const { netWorkPath, filePath } = data;
                    file.url = netWorkPath;
                    (file as any).shortOssUrl = filePath;
                  }
                }
              });
            },
          }}
        />
      </ProForm>
      <Button
        type="primary"
        className={styles['append-info-btn']}
        onClick={handleSubmit}
        disabled={mode === 'read'}
      >
        提交后补材料
      </Button>
      {/*  */}
      <ImagePreview ref={previewRef} />
    </div>
  ) : null;
});

export default AppendInfo;
