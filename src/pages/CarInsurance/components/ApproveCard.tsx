import { ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons';
import { Button, Card, Form, theme } from 'antd';
import React, { useMemo, useState } from 'react';
import styles from '../styles/index.less';

import { EcarInsuranceStatus } from '@/utils/bankend/enum';
import { ProFormRadio, ProFormTextArea } from '@ant-design/pro-components';
import { useAccess, useModel } from '@umijs/max';

const { useToken } = theme;

interface ApproveCardProps {
  beforeExpand?: () => Promise<boolean>;
  beforeSubmit: (values: any) => Promise<void>;
}

const ApproveCard: React.FC<ApproveCardProps> = ({
  beforeExpand = () => Promise.resolve(),
  beforeSubmit,
}) => {
  const { token } = useToken();
  const [form] = Form.useForm();
  const [isExpand, setExpand] = useState(false);
  const access = useAccess();
  const { detailData } = useModel('CarInsurance.carInsurance');
  const { initialState } = useModel<'@@initialState'>('@@initialState');
  const { pid } = initialState?.currentUser || {};

  const displayComp = useMemo(() => {
    if (
      detailData?.orderStatus === EcarInsuranceStatus.PENDING &&
      access.hasAccess('show_order_approval_windows_detail_car_insurance_list')
    ) {
      return true;
    }
    return false;
  }, [detailData]);
  const handleExpand = () => {
    if (isExpand) {
      setExpand(false);
    } else {
      beforeExpand().then(() => {
        setExpand(true);
      });
    }
  };
  const handleSubmit = () => {
    form.validateFields().then((values) => {
      beforeSubmit(values).then(() => {
        form.resetFields();
      });
    });
  };
  return displayComp ? (
    <div className={styles['approve-card']}>
      <Card
        title="订单审核"
        bordered={false}
        extra={
          <span
            style={{ color: '#fff', fontSize: '20px', cursor: 'pointer' }}
            onClick={handleExpand}
          >
            {isExpand ? <ShrinkOutlined /> : <ArrowsAltOutlined />}
          </span>
        }
        styles={{
          header: {
            background: token.colorPrimary,
            color: token.colorTextLightSolid,
            width: '340px',
            borderRadius: '2px',
            minHeight: '48px',
          },
          body: {
            display: isExpand ? 'block' : 'none',
            maxHeight: '430px',
            overflowY: 'auto',
            width: '340px',
          },
        }}
      >
        <Form
          form={form}
          layout="horizontal"
          labelAlign="right"
          size="small"
          labelCol={{ span: 7 }}
        >
          <ProFormRadio.Group
            name="userBehaviorEnum"
            label="审核结果"
            rules={[{ required: true, message: '请选择审核结果!' }]}
            options={[
              {
                label: '通过',
                value: 'SUBMIT_PASS',
              },
              {
                label: '驳回',
                value: 'SUBMIT_FOR_REVIEW_REJECT',
              },
            ]}
          />

          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) => {
              const userBehaviorEnumValue = getFieldValue('userBehaviorEnum');
              return userBehaviorEnumValue ? (
                <>
                  {userBehaviorEnumValue === 'SUBMIT_FOR_REVIEW_REJECT' ? (
                    <ProFormTextArea
                      name="rejectMsg"
                      label={<div>驳回原因 :</div>}
                      formItemProps={{
                        labelCol: {
                          span: 24,
                        },
                        layout: 'vertical',
                      }}
                      fieldProps={{
                        maxLength: 600,
                        showCount: true,
                      }}
                      rules={[{ required: true, message: '驳回原因不能为空' }]}
                    />
                  ) : (
                    <ProFormTextArea
                      label={
                        <div>
                          内部备注
                          <Button type="link" style={{ padding: 0 }}>
                            (渠道无法查看)
                          </Button>
                          :
                        </div>
                      }
                      name="remarkMsg"
                      formItemProps={{
                        labelCol: {
                          span: 24,
                        },
                        layout: 'vertical',
                      }}
                      fieldProps={{
                        maxLength: 30,
                        showCount: true,
                      }}
                    />
                  )}
                </>
              ) : null;
            }}
          </Form.Item>
          <Form.Item style={{ textAlign: 'right', marginBottom: '0' }}>
            <Button
              type="primary"
              disabled={!(pid === detailData?.operatorUserId)}
              onClick={handleSubmit}
            >
              提交
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  ) : null;
};

export default ApproveCard;
