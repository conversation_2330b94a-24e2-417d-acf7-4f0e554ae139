import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import {
  closestCenter,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Col, Row, Tag } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useCarInsuranceBulkOcrContext } from '../../context/CarInsuranceBulkOcrContext';
import { getFileNameFromUrl, getOssPathFromUrl, getRandomUUID } from '../../utils';
import DraggableItem from './DraggableItem';
import './index.less';
import SidebarList from './SidebarList';
import SourceArea from './SourceArea';
import TargetZone from './TargetZone';

export interface DragItem {
  id: string;
  name: string;
  type?: 'image' | 'file';
  size?: string;
  url?: string; // 添加文件URL用于预览
  filePath?: string; // oss短路径
}

export interface ListItem {
  id: string;
  vin: string;
  count: number;
  plateNo: string;
}

interface DragDropZonesProps {
  onDataChange?: (data: {
    globalSourceFiles: DragItem[]; // 全局源文件（notMatchFileDTOList）
    currentCategoryData: {
      targetZone1: DragItem[];
      targetZone2: DragItem[];
    };
    formattedApiData?: Record<
      string,
      {
        vehicleLicenseUrlList: string[];
        vehicleLicenseOssUrlList: string[];
        insuranceSlipUrlList: string[];
        insuranceSlipOssUrlList: string[];
      }
    >;
  }) => void;
}

const DragDropZones: React.FC<DragDropZonesProps> = ({ onDataChange }) => {
  // 获取批量OCR上下文数据
  const { batchOcrData } = useCarInsuranceBulkOcrContext();

  // 将文件item转换为DragItem格式
  const convertUrlToDragItem = useCallback((item: any, index: number): DragItem => {
    const fileName = getFileNameFromUrl(item?.url);
    return {
      id: `${item?.id}_${index}`, // 使用URL和索引作为唯一ID
      name: fileName,
      type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
      url: item?.url,
      filePath: item?.filePath || getOssPathFromUrl(item?.url || ''), // 如果没有filePath，从URL中提取
    };
  }, []);

  // 根据carInfoDTOList格式化数据
  const formatCarInfoData = useCallback(() => {
    const categoryDataMap: Record<
      string,
      {
        targetZone1: DragItem[];
        targetZone2: DragItem[];
      }
    > = {};

    const listItems: ListItem[] = [];

    // 处理carInfoDTOList中的每个车辆
    batchOcrData.carInfoDTOList?.forEach((carInfo: any) => {
      const vin = carInfo.vin || '无车架号';
      const plateNo = carInfo.plateNo || '无车牌号';

      // 处理车辆证件 (vehicleLicenseOssUrlList -> targetZone1)
      // 如果是团单(groupFlag=1)，需要为每个车辆生成唯一的id，避免重复
      const vehicleDocuments =
        carInfo.vehicleLicenseInfoList?.map((item: any, index: number) => {
          const uniqueId =
            batchOcrData.groupFlag === 1
              ? `${item?.filePath}_${carInfo?.id}_${index}_${getRandomUUID(8)}` // 团单时生成唯一ID
              : `${item?.filePath}_${index}`; // 非团单时使用原逻辑

          const fileName = getFileNameFromUrl(item?.url);
          return {
            id: uniqueId,
            name: fileName,
            type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
            url: item?.url,
            filePath: item?.filePath,
          } as DragItem;
        }) || [];

      // 处理投保材料 (insuranceSlipOssUrlList -> targetZone2)
      const insuranceMaterials =
        carInfo.insuranceSlipInfoList?.map((item: any, index: number) => {
          const uniqueId =
            batchOcrData.groupFlag === 1
              ? `${item?.filePath}_${carInfo?.id}_${index}_${getRandomUUID(8)}` // 团单时生成唯一ID
              : `${item?.filePath}_${index}`; // 非团单时使用原逻辑

          const fileName = getFileNameFromUrl(item?.url);
          return {
            id: uniqueId,
            name: fileName,
            type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
            url: item?.url,
            filePath: item?.filePath,
          } as DragItem;
        }) || [];

      // 初始化该VIN的数据结构（移除sourceFiles字段）
      categoryDataMap[carInfo.id] = {
        targetZone1: vehicleDocuments, // 车辆证件
        targetZone2: insuranceMaterials, // 投保材料
      };

      // 添加到列表项
      listItems.push({
        id: carInfo.id,
        vin: vin,
        plateNo: plateNo,
        count: vehicleDocuments.length + insuranceMaterials.length,
      });
    });

    // 处理未匹配的文件作为全局源文件
    const globalSourceFiles: DragItem[] = [];
    batchOcrData.notMatchFileDTOList?.forEach((file: any, index: number) => {
      if (file.url) {
        globalSourceFiles.push(convertUrlToDragItem(file, index));
      }
    });

    return { categoryDataMap, listItems, globalSourceFiles };
  }, [batchOcrData, convertUrlToDragItem]);

  // 获取格式化后的数据
  const {
    categoryDataMap: formattedCategoryDataMap,
    listItems: formattedListItems,
    globalSourceFiles: formattedGlobalSourceFiles,
  } = useMemo(() => {
    return formatCarInfoData();
  }, [formatCarInfoData]);

  const [selectedListId, setSelectedListId] = useState<string | null>(
    formattedListItems.length > 0 ? formattedListItems[0].id : null,
  );
  const [activeId, setActiveId] = useState<string | null>(null);

  // 预览相关
  const previewRef = useRef<ImagePreviewInstance>(null);

  // 所有分类的数据状态 - 使用格式化后的数据初始化（移除sourceFiles字段）
  const [categoryDataMap, setCategoryDataMap] = useState<
    Record<
      string,
      {
        targetZone1: DragItem[];
        targetZone2: DragItem[];
      }
    >
  >(formattedCategoryDataMap);

  // 全局源文件状态 - 用于显示notMatchFileDTOList
  const [globalSourceFiles, setGlobalSourceFiles] = useState<DragItem[]>(
    formattedGlobalSourceFiles,
  );

  // 更新categoryDataMap和globalSourceFiles当格式化数据变化时
  useEffect(() => {
    setCategoryDataMap(formattedCategoryDataMap);
    setGlobalSourceFiles(formattedGlobalSourceFiles);
  }, [formattedCategoryDataMap, formattedGlobalSourceFiles]);

  // 组件卸载时清除数据
  useEffect(() => {
    return () => {
      console.log('组件卸载');
      // 清除一些旧数据等
      setCategoryDataMap({});
      setGlobalSourceFiles([]);
      setSelectedListId(null);
      setActiveId(null);
    };
  }, []);

  // 分类列表项（使用格式化后的数据）
  const listItems = useMemo(() => {
    return formattedListItems.map((item) => ({
      ...item,
      count:
        // (categoryDataMap[item.id]?.sourceFiles.length || 0) +
        (categoryDataMap[item.id]?.targetZone1.length || 0) +
        (categoryDataMap[item.id]?.targetZone2.length || 0),
    }));
  }, [formattedListItems, categoryDataMap]);

  // 格式化数据为API提交格式
  const formatDataForSubmission = useCallback((new_categoryDataMap) => {
    const formattedData: Record<
      string,
      {
        vehicleLicenseUrlList: string[];
        vehicleLicenseOssUrlList: string[];
        insuranceSlipUrlList: string[];
        insuranceSlipOssUrlList: string[];
      }
    > = {};

    Object.keys(new_categoryDataMap).forEach((id) => {
      const categoryData = new_categoryDataMap[id];

      // 提取车辆证件oss短路径 (targetZone1)
      const vehicleLicenseOssUrlList = categoryData.targetZone1
        .map((item) => item.filePath || '') // 如果没有filePath，使用空字符串
        .filter(Boolean) as string[];
      // 提取车辆证件url
      const vehicleLicenseUrlList = categoryData.targetZone1
        .map((item) => item.url || '') // 如果没有url，使用空字符串
        .filter(Boolean) as string[];

      // 提取投保材料oss短路径 (targetZone2)
      const insuranceSlipOssUrlList = categoryData.targetZone2
        .map((item) => item.filePath || '') // 如果没有filePath，使用空字符串
        .filter(Boolean) as string[];
      // 提取投保材料url
      const insuranceSlipUrlList = categoryData.targetZone2
        .map((item) => item.url || '') // 如果没有url，使用空字符串
        .filter(Boolean) as string[];

      formattedData[id] = {
        vehicleLicenseUrlList,
        vehicleLicenseOssUrlList,
        insuranceSlipUrlList,
        insuranceSlipOssUrlList,
      };
    });

    return formattedData;
  }, []);

  // 预览事件监听
  useEffect(() => {
    const handlePreviewFile = (event: CustomEvent) => {
      console.log('预览事件触发:', event.detail);
      const { url, fileName } = event.detail;
      if (previewRef.current) {
        previewRef.current.previewFile({
          url,
          fileName,
        });
      } else {
        console.error('预览组件引用不存在');
      }
    };

    window.addEventListener('drog-item-preview-file', handlePreviewFile as EventListener);

    return () => {
      window.removeEventListener('drog-item-preview-file', handlePreviewFile as EventListener);
    };
  }, []);

  // 配置拖拽传感器，避免点击就触发拖拽
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽8px距离才激活
      },
    }),
    useSensor(KeyboardSensor),
  );

  // 获取当前选中分类的数据
  const getCurrentCategoryData = useCallback(() => {
    if (!selectedListId || !categoryDataMap[selectedListId]) {
      return { targetZone1: [], targetZone2: [] };
    }
    return categoryDataMap[selectedListId];
  }, [selectedListId, categoryDataMap]);

  // 获取当前显示的源文件 - (notMatchFileDTOList)
  const getDisplayedSourceFiles = useCallback(() => {
    return globalSourceFiles;
  }, [globalSourceFiles]);

  // 找到项目所在的区域
  const findItemLocation = useCallback(
    (id: string) => {
      const currentData = getCurrentCategoryData();

      // 首先在全局源文件中查找
      if (globalSourceFiles.find((item) => item.id === id)) {
        return 'source';
      }

      if (currentData.targetZone1.find((item) => item.id === id)) {
        return 'targetZone1';
      }

      if (currentData.targetZone2.find((item) => item.id === id)) {
        return 'targetZone2';
      }

      return null;
    },
    [getCurrentCategoryData, globalSourceFiles],
  );

  // 获取拖拽的项目
  const getActiveItem = useCallback(() => {
    if (!activeId) return null;

    const currentData = getCurrentCategoryData();

    // 首先从全局源文件中查找
    const globalSourceItem = globalSourceFiles.find((item) => item.id === activeId);
    if (globalSourceItem) return globalSourceItem;

    // 从目标区域中查找
    const targetItem1 = currentData.targetZone1.find((item) => item.id === activeId);
    if (targetItem1) return targetItem1;

    const targetItem2 = currentData.targetZone2.find((item) => item.id === activeId);
    if (targetItem2) return targetItem2;

    return null;
  }, [activeId, getCurrentCategoryData, globalSourceFiles]);

  // 开始拖拽
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // 结束拖拽
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);

    if (!over || !selectedListId) {
      return;
    }

    const c_activeId = active.id as string;
    const overId = over.id as string;

    // 如果拖拽到同一个位置，不做任何操作
    const sourceLocation = findItemLocation(c_activeId);
    if (sourceLocation === overId) {
      return;
    }

    const currentData = getCurrentCategoryData();
    let draggedItem: DragItem | undefined;

    // 找到被拖拽的项目
    if (sourceLocation === 'source') {
      // 从全局源文件中查找
      draggedItem = globalSourceFiles.find((item) => item.id === c_activeId);
    } else if (sourceLocation === 'targetZone1') {
      draggedItem = currentData.targetZone1.find((item) => item.id === c_activeId);
    } else if (sourceLocation === 'targetZone2') {
      draggedItem = currentData.targetZone2.find((item) => item.id === c_activeId);
    }

    // 只有在找到项目且目标区域有效时才进行移动
    if (
      draggedItem &&
      (overId === 'source' || overId === 'targetZone1' || overId === 'targetZone2')
    ) {
      // 判断是否从全局源文件拖拽
      const isFromGlobalSource = sourceLocation === 'source';

      if (isFromGlobalSource) {
        // 从全局源文件拖拽到目标区域
        if (overId !== 'source') {
          setGlobalSourceFiles((prev) => prev.filter((item) => item.id !== c_activeId));
        }
      }

      setCategoryDataMap((prev) => {
        const newData = { ...prev[selectedListId] };

        // 从原位置移除（仅处理目标区域之间的移动）
        if (sourceLocation === 'targetZone1') {
          newData.targetZone1 = newData.targetZone1.filter((item) => item.id !== c_activeId);
        } else if (sourceLocation === 'targetZone2') {
          newData.targetZone2 = newData.targetZone2.filter((item) => item.id !== c_activeId);
        }

        // 添加到新位置
        if (overId === 'targetZone1') {
          newData.targetZone1 = [...newData.targetZone1, draggedItem];
        } else if (overId === 'targetZone2') {
          newData.targetZone2 = [...newData.targetZone2, draggedItem];
        } else if (overId === 'source') {
          // 拖拽回源区域时，添加到全局源文件
          setGlobalSourceFiles((prev) => [...prev, draggedItem]);
        }

        const updatedData = {
          ...prev,
          [selectedListId]: newData,
        };

        // 计算最新的全局源文件状态
        let updatedGlobalSourceFiles = globalSourceFiles;
        if (isFromGlobalSource && overId !== 'source') {
          // 从源文件拖拽到目标区域
          updatedGlobalSourceFiles = globalSourceFiles.filter((item) => item.id !== c_activeId);
        } else if (!isFromGlobalSource && overId === 'source') {
          // 从目标区域拖拽回源文件
          updatedGlobalSourceFiles = [...globalSourceFiles, draggedItem];
        }

        // 触发数据变更回调，包含格式化后的API数据
        const formattedApiData = formatDataForSubmission(updatedData);

        onDataChange?.({
          globalSourceFiles: updatedGlobalSourceFiles,
          currentCategoryData: {
            targetZone1: newData.targetZone1,
            targetZone2: newData.targetZone2,
          },
          formattedApiData,
        });

        return updatedData;
      });
    }
  };

  // 删除目标区域中的项目
  const handleDeleteItem = useCallback(
    (itemId: string) => {
      if (!selectedListId) return;

      setCategoryDataMap((prev) => {
        const currentData = prev[selectedListId];
        let updatedData = { ...currentData };

        // 检查是否在目标区域1中
        if (currentData.targetZone1.find((item) => item.id === itemId)) {
          updatedData = {
            ...updatedData,
            targetZone1: currentData.targetZone1.filter((item) => item.id !== itemId),
          };
        }
        // 检查是否在目标区域2中
        else if (currentData.targetZone2.find((item) => item.id === itemId)) {
          updatedData = {
            ...updatedData,
            targetZone2: currentData.targetZone2.filter((item) => item.id !== itemId),
          };
        }

        const newCategoryDataMap = {
          ...prev,
          [selectedListId]: updatedData,
        };

        // 触发数据变更回调，包含格式化后的API数据
        const formattedApiData = formatDataForSubmission(newCategoryDataMap);
        onDataChange?.({
          globalSourceFiles,
          currentCategoryData: {
            targetZone1: updatedData.targetZone1,
            targetZone2: updatedData.targetZone2,
          },
          formattedApiData,
        });

        return newCategoryDataMap;
      });
    },
    [selectedListId, onDataChange, globalSourceFiles, formatDataForSubmission],
  );

  return (
    <div className="drag-drop-zones-new">
      <DndContext
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        sensors={sensors}
      >
        {/* 上方：源文件区域 */}
        <div className="top-section">
          <SourceArea items={getDisplayedSourceFiles()} />
        </div>

        {/* 下方：左侧列表 + 右侧目标区域 */}
        <div className="bottom-section">
          <Row gutter={16}>
            {/* 左侧：分类列表 */}
            <Col span={8}>
              <SidebarList
                items={listItems}
                selectedId={selectedListId}
                onSelect={setSelectedListId}
              />
            </Col>

            {/* 右侧：两个目标区域 */}
            <Col span={16}>
              <div className="target-zones">
                <div className="target-zones-header">
                  <span>当前车架号：</span>
                  {selectedListId && (
                    <Tag color="orange">
                      {listItems.find((item) => item.id === selectedListId)?.vin}
                    </Tag>
                  )}
                </div>
                <Row gutter={16}>
                  <Col span={12}>
                    <TargetZone
                      id="targetZone1"
                      title="车辆证件"
                      items={getCurrentCategoryData().targetZone1}
                      onDeleteItem={handleDeleteItem}
                    />
                  </Col>
                  <Col span={12}>
                    <TargetZone
                      id="targetZone2"
                      title="投保材料"
                      items={getCurrentCategoryData().targetZone2}
                      onDeleteItem={handleDeleteItem}
                    />
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </div>

        {/* 拖拽预览层 */}
        <DragOverlay>
          {activeId ? (
            <div className="drag-overlay-new">
              <DraggableItem item={getActiveItem()!} isDragging />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* 文件预览组件 - 不占用布局空间 */}
      <ImagePreview ref={previewRef} />
    </div>
  );
};

export default DragDropZones;
